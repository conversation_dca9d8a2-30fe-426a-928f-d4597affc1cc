<?php
session_start();

// 檢查用戶是否已登入且為管理員
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// 產生 CSRF Token，用於保護表單和 AJAX 請求
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

require_once 'php/DatabaseMySQLi.php';
$db = new Database();
$conn = $db->getConnection();

// 獲取所有用戶
$users_query = "SELECT *, DATEDIFF(trial_end_date, NOW()) as days_remaining FROM users ORDER BY created_at DESC";
$users_result = $conn->query($users_query);

// 獲取統計信息
$stats_query = "
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_users,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
        SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admin_users,
        SUM(COALESCE(credits, 0)) as total_credits,
        AVG(COALESCE(credits, 0)) as avg_credits
    FROM users
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>
<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- CSRF Token Meta 標籤，供 JavaScript 讀取 -->
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    <title>管理員面板 - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/admin.css" rel="stylesheet">
    <link href="css/custom-modal.css" rel="stylesheet">
</head>
<body class="admin-body">
    <div class="admin-container">
        <!-- 頂部導航 -->
        <nav class="admin-navbar">
            <div class="admin-nav-brand">
                <span class="admin-icon">⚙️</span>
                <h1>管理員面板</h1>
            </div>
            <div class="admin-nav-actions">
                <a href="index.php" class="btn btn-outline-primary">返回首頁</a>
                <?php if (isset($_SESSION['original_user_id'])): ?>
                    <button class="btn btn-warning" data-action="return-to-admin">返回管理員身份</button>
                <?php endif; ?>
                <a href="logout.php" class="btn btn-outline-danger">登出</a>
            </div>
        </nav>
        
        <!-- 主要內容 -->
        <div class="admin-content">
            <!-- 統計卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info"><h3><?php echo $stats['total_users']; ?></h3><p>總用戶數</p></div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-info"><h3><?php echo $stats['pending_users']; ?></h3><p>待審核</p></div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-info"><h3><?php echo $stats['active_users']; ?></h3><p>活躍用戶</p></div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👑</div>
                    <div class="stat-info"><h3><?php echo $stats['admin_users']; ?></h3><p>管理員</p></div>
                </div>
            </div>

            <!-- 試用管理部分 -->
            <div class="admin-section">
                <div class="section-header">
                    <h2>試用系統管理</h2>
                    <button class="btn btn-primary" data-action="open-trial-settings-modal">試用設定</button>
                </div>
                <div class="trial-stats-grid">
                    <div class="trial-stat-card"><div class="stat-icon">⏰</div><div class="stat-info"><h3 id="activeTrialsCount">-</h3><p>啟用中</p></div></div>
                    <div class="trial-stat-card"><div class="stat-icon">⚠️</div><div class="stat-info"><h3 id="expiringTrialsCount">-</h3><p>即將到期</p></div></div>
                    <div class="trial-stat-card"><div class="stat-icon">❌</div><div class="stat-info"><h3 id="expiredTrialsCount">-</h3><p>已過期</p></div></div>
                    <div class="trial-stat-card"><div class="stat-icon">✅</div><div class="stat-info"><h3 id="convertedTrialsCount">-</h3><p>已轉換</p></div></div>
                </div>
            </div>

            <!-- 用戶管理表格 -->
            <div class="admin-section">
                <div class="section-header">
                    <h2>用戶管理</h2>
                    <div class="d-flex gap-2">
                        <input type="search" id="userSearch" class="form-control" placeholder="搜尋用戶名、Email...">
                        <button class="btn btn-success flex-shrink-0" data-action="open-add-user-modal">新增用戶</button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用戶名</th>
                                <th>Email</th>
                                <th>姓名</th>
                                <th>類型</th>
                                <th>帳號狀態</th>
                                <th>試用狀態</th>
                                <th>點數</th>
                                <th>註冊日期</th>
                                <th style="width: 280px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($user = $users_result->fetch_assoc()): ?>
                            <tr id="user-row-<?php echo $user['id']; ?>">
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></td>
                                <td><span class="badge bg-<?php echo $user['user_type'] === 'admin' ? 'primary' : 'secondary'; ?>"><?php echo ucfirst($user['user_type']); ?></span></td>
                                <td>
                                    <?php
                                        $status_map = ['active' => 'success', 'pending' => 'warning', 'inactive' => 'danger'];
                                        $status_class = $status_map[$user['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?>"><?php echo ucfirst($user['status']); ?></span>
                                </td>
                                <td>
                                    <?php if ($user['is_trial_user']): ?>
                                        <span class="badge bg-info">試用中 (剩 <?php echo max(0, $user['days_remaining']); ?> 天)</span>
                                    <?php else: ?>
                                        <span class="badge bg-purple">付費會員</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="credits-display"><?php echo number_format($user['credits'] ?? 0); ?></span>
                                    <button class="btn btn-sm btn-outline-primary ms-1" data-action="open-credits-modal" data-user-id="<?php echo $user['id']; ?>" data-credits="<?php echo $user['credits'] ?? 0; ?>" data-username="<?php echo htmlspecialchars($user['username']); ?>" title="管理點數">💰</button>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <?php if ($user['status'] === 'pending'): ?>
                                            <button class="btn btn-sm btn-success" data-action="approve-user" data-user-id="<?php echo $user['id']; ?>">批准</button>
                                            <button class="btn btn-sm btn-warning" data-action="reject-user" data-user-id="<?php echo $user['id']; ?>">拒絕</button>
                                        <?php endif; ?>
                                        <button class="btn btn-sm btn-primary" data-action="edit-user" data-user-data='<?php echo htmlspecialchars(json_encode($user), ENT_QUOTES, 'UTF-8'); ?>'>編輯</button>
                                        <?php if ($user['user_type'] === 'member' && $user['status'] === 'active'): ?>
                                            <button class="btn btn-sm btn-info" data-action="proxy-login" data-user-id="<?php echo $user['id']; ?>" data-username="<?php echo htmlspecialchars($user['username']); ?>" title="代理登入">代理登入</button>
                                        <?php endif; ?>
                                        <?php if (($user['user_type'] !== 'admin' || $user['id'] != $_SESSION['user_id'])): ?>
                                            <button class="btn btn-sm btn-danger" data-action="delete-user" data-user-id="<?php echo $user['id']; ?>">刪除</button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模態框 (Modals) -->
    <!-- 編輯用戶模態框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="editUserForm">
                    <div class="modal-header">
                        <h5 class="modal-title">編輯用戶</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_user">
                        <input type="hidden" name="user_id" id="edit_user_id">
                        <div class="row">
                            <div class="col-md-6 mb-3"><label class="form-label">用戶名</label><input type="text" class="form-control" name="username" id="edit_username" required></div>
                            <div class="col-md-6 mb-3"><label class="form-label">Email</label><input type="email" class="form-control" name="email" id="edit_email" required></div>
                            <div class="col-md-6 mb-3"><label class="form-label">名字</label><input type="text" class="form-control" name="first_name" id="edit_first_name"></div>
                            <div class="col-md-6 mb-3"><label class="form-label">姓氏</label><input type="text" class="form-control" name="last_name" id="edit_last_name"></div>
                            <div class="col-md-4 mb-3"><label class="form-label">電話</label><input type="tel" class="form-control" name="phone" id="edit_phone"></div>
                            <div class="col-md-4 mb-3"><label class="form-label">用戶類型</label><select class="form-select" name="user_type" id="edit_user_type"><option value="member">Member</option><option value="admin">Admin</option></select></div>
                            <div class="col-md-4 mb-3"><label class="form-label">狀態</label><select class="form-select" name="status" id="edit_status"><option value="pending">Pending</option><option value="active">Active</option><option value="inactive">Inactive</option></select></div>
                            <div class="col-12 mb-3"><label class="form-label">重設密碼 (留空則不更改)</label><input type="password" class="form-control" name="new_password" placeholder="輸入新密碼"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">儲存變更</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 新增用戶模態框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="addUserForm">
                     <div class="modal-header"><h5 class="modal-title">新增用戶</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>
                    <div class="modal-body">
                         <div class="row">
                            <div class="col-md-6 mb-3"><label class="form-label">用戶名 *</label><input type="text" class="form-control" name="username" required></div>
                            <div class="col-md-6 mb-3"><label class="form-label">Email *</label><input type="email" class="form-control" name="email" required></div>
                            <div class="col-md-6 mb-3"><label class="form-label">密碼 *</label><input type="password" class="form-control" name="password" required></div>
                            <div class="col-md-6 mb-3"><label class="form-label">名字</label><input type="text" class="form-control" name="first_name"></div>
                            <div class="col-md-6 mb-3"><label class="form-label">姓氏</label><input type="text" class="form-control" name="last_name"></div>
                            <div class="col-md-6 mb-3"><label class="form-label">電話</label><input type="tel" class="form-control" name="phone"></div>
                            <div class="col-md-4 mb-3"><label class="form-label">用戶類型</label><select class="form-select" name="user_type"><option value="member">Member</option><option value="admin">Admin</option></select></div>
                            <div class="col-md-4 mb-3"><label class="form-label">狀態</label><select class="form-select" name="status"><option value="active">Active</option><option value="pending">Pending</option><option value="inactive">Inactive</option></select></div>
                            <div class="col-md-4 mb-3"><label class="form-label">初始點數</label><input type="number" class="form-control" name="initial_credits" value="10" min="0"></div>
                        </div>
                    </div>
                    <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button><button type="submit" class="btn btn-primary">確認新增</button></div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 點數管理模態框 -->
    <div class="modal fade" id="creditsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="creditsForm">
                    <div class="modal-header"><h5 class="modal-title">管理點數</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>
                    <div class="modal-body">
                        <input type="hidden" name="user_id" id="credits_user_id">
                        <div class="mb-3"><label class="form-label">用戶</label><input type="text" class="form-control" id="credits_username" readonly></div>
                        <div class="mb-3"><label class="form-label">目前點數</label><input type="text" class="form-control" id="credits_current" readonly></div>
                        <div class="mb-3"><label class="form-label">操作</label><select class="form-select" name="credits_action" id="credits_action" required><option value="" disabled selected>請選擇操作</option><option value="add">增加點數</option><option value="deduct">扣除點數</option></select></div>
                        <div class="mb-3"><label class="form-label">數量</label><input type="number" class="form-control" name="credits_amount" id="credits_amount" min="1" required></div>
                        <div class="mb-3"><label class="form-label">原因</label><textarea class="form-control" name="credits_reason" id="credits_reason" rows="3" required></textarea></div>
                    </div>
                    <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button><button type="submit" class="btn btn-primary">更新點數</button></div>
                </form>
            </div>
        </div>
    </div>

    <!-- 通用 Modal 模板 -->
    <template id="confirmModalTemplate">
        <div class="modal fade" tabindex="-1"><div class="modal-dialog modal-dialog-centered"><div class="modal-content">
            <div class="modal-header"><h5 class="modal-title"></h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary btn-confirm">確認</button>
            </div>
        </div></div></div>
    </template>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>