<?php
/**
 * 獲取系統配置
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 系統配置數據
    $systemConfig = [
        'app_name' => 'KMS Receipt Maker',
        'version' => '1.0.0',
        'default_language' => 'en',
        'supported_languages' => ['en', 'zh'],
        'currency' => 'USD',
        'timezone' => 'Asia/Hong_Kong',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i:s',
        'max_receipt_items' => 50,
        'max_file_size' => 5242880, // 5MB
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
        'pagination_limit' => 20,
        'session_timeout' => 3600, // 1 hour
        'password_min_length' => 6,
        'enable_registration' => true,
        'enable_password_reset' => true,
        'maintenance_mode' => false,
        'debug_mode' => false
    ];
    
    // 如果用戶已登錄，可以添加用戶特定的配置
    if (isset($_SESSION['user_id'])) {
        $systemConfig['user_logged_in'] = true;
        $systemConfig['user_id'] = $_SESSION['user_id'];
        
        // 用戶特定設置（從 localStorage 或 session 獲取）
        // 語言偏好從前端 localStorage 管理，不需要從數據庫查詢
    } else {
        $systemConfig['user_logged_in'] = false;
    }
    
    Response::success($systemConfig, '系統配置載入成功');
    
} catch (Exception $e) {
    error_log('Get system config error: ' . $e->getMessage());
    Response::error('載入系統配置失敗: ' . $e->getMessage());
}
?>