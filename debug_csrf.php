<?php
session_start();

// Debug CSRF token and session information
header('Content-Type: application/json; charset=utf-8');

$debug_info = [
    'session_started' => session_status() === PHP_SESSION_ACTIVE,
    'session_id' => session_id(),
    'csrf_token_exists' => isset($_SESSION['csrf_token']),
    'csrf_token_length' => isset($_SESSION['csrf_token']) ? strlen($_SESSION['csrf_token']) : 0,
    'csrf_token_preview' => isset($_SESSION['csrf_token']) ? substr($_SESSION['csrf_token'], 0, 10) . '...' : 'N/A',
    'user_logged_in' => isset($_SESSION['user_id']),
    'user_type' => $_SESSION['user_type'] ?? 'N/A',
    'post_data' => $_POST,
    'request_method' => $_SERVER['REQUEST_METHOD']
];

// If this is a POST request, test CSRF validation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $debug_info['csrf_validation'] = [
        'post_token_exists' => isset($_POST['csrf_token']),
        'post_token_length' => isset($_POST['csrf_token']) ? strlen($_POST['csrf_token']) : 0,
        'post_token_preview' => isset($_POST['csrf_token']) ? substr($_POST['csrf_token'], 0, 10) . '...' : 'N/A',
        'tokens_match' => isset($_POST['csrf_token']) && isset($_SESSION['csrf_token']) && 
                         hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
    ];
}

echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
