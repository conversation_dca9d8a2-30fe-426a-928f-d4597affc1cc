<?php
/**
 * 獲取用戶試用信息
 * KMS Receipt Maker - Get Trial Information
 */

session_start();

require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';
require_once 'TrialManager.php';

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::error('User not logged in');
    exit;
}

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('Method not allowed');
    exit;
}

$action = $_POST['action'] ?? '';
$userId = $_SESSION['user_id'];

try {
    $trialManager = new TrialManager();
    
    switch ($action) {
        case 'get_user_trial_info':
            // 獲取用戶試用信息
            $trialInfo = $trialManager->getUserTrialInfo($userId);
            $isTrialUser = $trialManager->isTrialUser($userId);
            $canUseFullFeatures = $trialManager->canUseFullFeatures($userId);
            
            Response::success([
                'trial_info' => $trialInfo,
                'is_trial_user' => $isTrialUser,
                'can_use_full_features' => $canUseFullFeatures
            ], 'Trial info retrieved successfully');
            break;
            
        case 'check_feature_access':
            $feature = $_POST['feature'] ?? '';
            
            if (empty($feature)) {
                Response::error('Feature not specified');
                exit;
            }
            
            $canUseFullFeatures = $trialManager->canUseFullFeatures($userId);
            $trialInfo = $trialManager->getUserTrialInfo($userId);
            
            // 檢查特定功能的訪問權限
            $hasAccess = true;
            $restrictedFeatures = ['save_receipt', 'print_receipt'];
            
            if (in_array($feature, $restrictedFeatures) && !$canUseFullFeatures) {
                $hasAccess = false;
            }
            
            Response::success([
                'has_access' => $hasAccess,
                'is_trial_user' => $trialManager->isTrialUser($userId),
                'trial_info' => $trialInfo,
                'feature' => $feature
            ], 'Feature access checked');
            break;
            
        case 'get_trial_status':
            $trialInfo = $trialManager->getUserTrialInfo($userId);
            $isTrialUser = $trialManager->isTrialUser($userId);
            
            $status = 'paid_member';
            $statusMessage = 'Full access available';
            
            if ($isTrialUser && $trialInfo) {
                if ($trialInfo['trial_status'] === 'expired' || $trialInfo['days_remaining'] < 0) {
                    $status = 'trial_expired';
                    $statusMessage = 'Trial period has expired';
                } elseif ($trialInfo['days_remaining'] <= 1) {
                    $status = 'trial_expires_today';
                    $statusMessage = 'Trial expires today';
                } elseif ($trialInfo['days_remaining'] <= 3) {
                    $status = 'trial_expires_soon';
                    $statusMessage = "Trial expires in {$trialInfo['days_remaining']} days";
                } elseif ($trialInfo['days_remaining'] <= 7) {
                    $status = 'trial_expires_this_week';
                    $statusMessage = "Trial expires in {$trialInfo['days_remaining']} days";
                } else {
                    $status = 'trial_active';
                    $statusMessage = "Trial active - {$trialInfo['days_remaining']} days remaining";
                }
            }
            
            Response::success([
                'status' => $status,
                'message' => $statusMessage,
                'trial_info' => $trialInfo,
                'is_trial_user' => $isTrialUser
            ], 'Trial status retrieved');
            break;
            
        case 'get_restricted_features':
            $isTrialUser = $trialManager->isTrialUser($userId);
            $canUseFullFeatures = $trialManager->canUseFullFeatures($userId);
            
            $restrictedFeatures = [];
            
            if ($isTrialUser && !$canUseFullFeatures) {
                $restrictedFeatures = [
                    'save_receipt' => [
                        'name' => 'Save Receipt',
                        'description' => 'Save receipts to your account',
                        'reason' => 'Not available during trial period'
                    ],
                    'print_receipt' => [
                        'name' => 'Print Receipt',
                        'description' => 'Print receipts directly',
                        'reason' => 'Not available during trial period'
                    ]
                ];
            }
            
            Response::success([
                'restricted_features' => $restrictedFeatures,
                'is_trial_user' => $isTrialUser,
                'can_use_full_features' => $canUseFullFeatures
            ], 'Restricted features retrieved');
            break;
            
        default:
            Response::error('Invalid action');
            break;
    }
    
} catch (Exception $e) {
    error_log("Trial info error: " . $e->getMessage());
    Response::error('System error occurred');
}
?>
