<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

// 統一的 JSON 響應函數
function send_json_response($success, $message, $data = null) {
    echo json_encode(['success' => $success, 'message' => $message, 'data' => $data]);
    exit();
}

// 1. 權限檢查：確保只有管理員可以訪問
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    http_response_code(403); // Forbidden
    send_json_response(false, '未經授權的存取。請重新登入。');
}

// 2. CSRF Token 驗證：防止跨站請求偽造攻擊
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        http_response_code(403); // Forbidden
        send_json_response(false, '無效的安全令牌，請刷新頁面後重試。');
    }
}

require_once 'php/DatabaseMySQLi.php';
$db = new Database();
$conn = $db->getConnection();

$action = $_POST['action'] ?? '';

// 3. 路由：根據 action 參數呼叫對應的處理函數
switch ($action) {
    case 'approve_user':
        updateUserStatus($conn, $_POST['user_id'] ?? 0, 'active', '用戶已成功批准。');
        break;

    case 'reject_user':
        updateUserStatus($conn, $_POST['user_id'] ?? 0, 'inactive', '用戶已成功拒絕。');
        break;
        
    case 'delete_user':
        handleDeleteUser($conn, $_POST['user_id'] ?? 0);
        break;

    case 'update_user':
        handleUpdateUser($conn, $_POST);
        break;

    case 'add_user':
        handleAddUser($conn, $_POST);
        break;
        
    case 'update_credits':
        handleUpdateCredits($conn, $_POST);
        break;
        
    default:
        send_json_response(false, '指定的 action 無效。');
}

// --- 處理函數 ---

function updateUserStatus($conn, $userId, $status, $message) {
    if (empty($userId) || !is_numeric($userId) || $userId <= 0) {
        send_json_response(false, '無效的用戶 ID。');
    }
    $stmt = $conn->prepare("UPDATE users SET status = ? WHERE id = ?");
    $stmt->bind_param('si', $status, $userId);
    if ($stmt->execute()) {
        send_json_response(true, $message);
    } else {
        send_json_response(false, '資料庫操作失敗：' . $stmt->error);
    }
}

function handleDeleteUser($conn, $userId) {
    if (empty($userId) || !is_numeric($userId) || $userId <= 0) {
        send_json_response(false, '無效的用戶 ID。');
    }
    if ($userId == $_SESSION['user_id']) {
        send_json_response(false, '您無法刪除自己的帳號。');
    }

    require_once 'php/UserDeletionManager.php';
    $deletionManager = new UserDeletionManager();
    $result = $deletionManager->deleteUserCompletely($userId, $_SESSION['user_id']);

    if ($result['success']) {
        send_json_response(true, '用戶及其所有相關資料已成功刪除。');
    } else {
        send_json_response(false, '刪除用戶失敗：' . $result['message']);
    }
}

function handleUpdateUser($conn, $postData) {
    $user_id = $postData['user_id'] ?? 0;
    if (empty($user_id)) send_json_response(false, '缺少用戶 ID。');

    $username = trim($postData['username'] ?? '');
    $email = trim($postData['email'] ?? '');
    $first_name = trim($postData['first_name'] ?? '');
    $last_name = trim($postData['last_name'] ?? '');
    $phone = trim($postData['phone'] ?? '');
    $user_type = $postData['user_type'] ?? 'member';
    $status = $postData['status'] ?? 'pending';
    $new_password = $postData['new_password'] ?? '';

    // 更新用戶基本信息
    $stmt = $conn->prepare("UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, phone = ?, user_type = ?, status = ? WHERE id = ?");
    $stmt->bind_param('sssssssi', $username, $email, $first_name, $last_name, $phone, $user_type, $status, $user_id);
    $stmt->execute();
    
    // 如果提供了新密碼，則更新密碼
    if (!empty($new_password)) {
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt_pass = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt_pass->bind_param('si', $password_hash, $user_id);
        $stmt_pass->execute();
    }
    
    send_json_response(true, '用戶資訊已成功更新。');
}

function handleAddUser($conn, $postData) {
    $username = trim($postData['username'] ?? '');
    $email = trim($postData['email'] ?? '');
    $password = $postData['password'] ?? '';

    if (empty($username) || empty($email) || empty($password)) {
        send_json_response(false, '用戶名、電子郵件和密碼為必填欄位。');
    }
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        send_json_response(false, '電子郵件格式不正確。');
    }

    // 檢查用戶名和郵箱是否已存在
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param('ss', $username, $email);
    $stmt->execute();
    if ($stmt->get_result()->num_rows > 0) {
        send_json_response(false, '此用戶名或電子郵件已被註冊。');
    }

    $first_name = trim($postData['first_name'] ?? '');
    $last_name = trim($postData['last_name'] ?? '');
    $phone = trim($postData['phone'] ?? '');
    $user_type = $postData['user_type'] ?? 'member';
    $status = $postData['status'] ?? 'active';
    $initial_credits = intval($postData['initial_credits'] ?? 10);
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt_insert = $conn->prepare("INSERT INTO users (username, email, password, first_name, last_name, phone, user_type, status, credits, email_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())");
    $stmt_insert->bind_param('ssssssssi', $username, $email, $password_hash, $first_name, $last_name, $phone, $user_type, $status, $initial_credits);
    
    if ($stmt_insert->execute()) {
        $user_id = $conn->insert_id;
        require_once 'php/CreditManager.php';
        $creditManager = new CreditManager($conn);
        $creditManager->addCredits($user_id, $initial_credits, 'admin_allocation', '管理員建立帳號時分配的初始點數');
        send_json_response(true, '用戶已成功新增。');
    } else {
        send_json_response(false, '新增用戶失敗：' . $stmt_insert->error);
    }
}

function handleUpdateCredits($conn, $postData) {
    $user_id = intval($postData['user_id'] ?? 0);
    $credits_action = $postData['credits_action'] ?? '';
    $credits_amount = intval($postData['credits_amount'] ?? 0);
    $credits_reason = trim($postData['credits_reason'] ?? '');

    if ($user_id <= 0 || $credits_amount <= 0 || empty($credits_reason) || !in_array($credits_action, ['add', 'deduct'])) {
        send_json_response(false, '無效的點數操作參數。');
    }
    
    require_once 'php/CreditManager.php';
    $creditManager = new CreditManager($conn);
    $result = false;
    $message = '';
    
    if ($credits_action === 'add') {
        $result = $creditManager->addCredits($user_id, $credits_amount, 'admin_allocation', $credits_reason);
        $message = $result ? '點數已成功增加。' : '增加點數失敗。';
    } else { // 'deduct'
        $result = $creditManager->deductCredits($user_id, $credits_amount, 'admin_deduction', $credits_reason);
        $message = $result ? '點數已成功扣除。' : '扣除點數失敗或餘額不足。';
    }

    if ($result) {
        send_json_response(true, $message, ['new_credits' => $creditManager->getCredits($user_id)]);
    } else {
        send_json_response(false, $message);
    }
}
?>