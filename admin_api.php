<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

// Unified JSON response function
function send_json_response($success, $message, $data = null) {
    echo json_encode(['success' => $success, 'message' => $message, 'data' => $data]);
    exit();
}

// 1. Permission check: Ensure only admins can access
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    http_response_code(403); // Forbidden
    send_json_response(false, 'Unauthorized access. Please log in again.');
}

// 2. CSRF Token verification: Prevent cross-site request forgery attacks
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if CSRF token exists and matches
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token'])) {
        http_response_code(403);
        send_json_response(false, 'Security token missing. Please refresh the page and try again.');
    }

    if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        http_response_code(403);
        send_json_response(false, 'Invalid security token. Please refresh the page and try again.');
    }
}

require_once 'php/DatabaseMySQLi.php';
$db = new Database();
$conn = $db->getConnection();

$action = $_POST['action'] ?? '';

// 3. Routing: Call corresponding handler function based on action parameter
switch ($action) {
    case 'approve_user':
        updateUserStatus($conn, $_POST['user_id'] ?? 0, 'active', 'User successfully approved.');
        break;

    case 'reject_user':
        updateUserStatus($conn, $_POST['user_id'] ?? 0, 'inactive', 'User successfully rejected.');
        break;
        
    case 'delete_user':
        handleDeleteUser($conn, $_POST['user_id'] ?? 0);
        break;

    case 'update_user':
        handleUpdateUser($conn, $_POST);
        break;

    case 'add_user':
        handleAddUser($conn, $_POST);
        break;
        
    case 'update_credits':
        handleUpdateCredits($conn, $_POST);
        break;

    case 'extend_trial':
        handleExtendTrial($conn, $_POST);
        break;

    default:
        send_json_response(false, 'Invalid action specified.');
}

// --- Handler Functions ---

function updateUserStatus($conn, $userId, $status, $message) {
    if (empty($userId) || !is_numeric($userId) || $userId <= 0) {
        send_json_response(false, 'Invalid user ID.');
    }
    $stmt = $conn->prepare("UPDATE users SET status = ? WHERE id = ?");
    $stmt->bind_param('si', $status, $userId);
    if ($stmt->execute()) {
        send_json_response(true, $message);
    } else {
        send_json_response(false, 'Database operation failed: ' . $stmt->error);
    }
}

function handleDeleteUser($conn, $userId) {
    if (empty($userId) || !is_numeric($userId) || $userId <= 0) {
        send_json_response(false, 'Invalid user ID.');
    }
    if ($userId == $_SESSION['user_id']) {
        send_json_response(false, 'You cannot delete your own account.');
    }

    require_once 'php/UserDeletionManager.php';
    $deletionManager = new UserDeletionManager();
    $result = $deletionManager->deleteUserCompletely($userId, $_SESSION['user_id']);

    if ($result['success']) {
        send_json_response(true, 'User and all related data successfully deleted.');
    } else {
        send_json_response(false, 'Failed to delete user: ' . $result['message']);
    }
}

function handleUpdateUser($conn, $postData) {
    $user_id = $postData['user_id'] ?? 0;
    if (empty($user_id)) send_json_response(false, 'Missing user ID.');

    $username = trim($postData['username'] ?? '');
    $email = trim($postData['email'] ?? '');
    $first_name = trim($postData['first_name'] ?? '');
    $last_name = trim($postData['last_name'] ?? '');
    $phone = trim($postData['phone'] ?? '');
    $user_type = $postData['user_type'] ?? 'member';
    $status = $postData['status'] ?? 'pending';
    $new_password = $postData['new_password'] ?? '';

    // Update user basic information
    $stmt = $conn->prepare("UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, phone = ?, user_type = ?, status = ? WHERE id = ?");
    $stmt->bind_param('sssssssi', $username, $email, $first_name, $last_name, $phone, $user_type, $status, $user_id);
    $stmt->execute();

    // If new password is provided, update password
    if (!empty($new_password)) {
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt_pass = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt_pass->bind_param('si', $password_hash, $user_id);
        $stmt_pass->execute();
    }

    send_json_response(true, 'User information successfully updated.');
}

function handleAddUser($conn, $postData) {
    $username = trim($postData['username'] ?? '');
    $email = trim($postData['email'] ?? '');
    $password = $postData['password'] ?? '';

    if (empty($username) || empty($email) || empty($password)) {
        send_json_response(false, 'Username, email, and password are required fields.');
    }
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        send_json_response(false, 'Invalid email format.');
    }

    // Check if username and email already exist
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param('ss', $username, $email);
    $stmt->execute();
    if ($stmt->get_result()->num_rows > 0) {
        send_json_response(false, 'This username or email is already registered.');
    }

    $first_name = trim($postData['first_name'] ?? '');
    $last_name = trim($postData['last_name'] ?? '');
    $phone = trim($postData['phone'] ?? '');
    $user_type = $postData['user_type'] ?? 'member';
    $status = $postData['status'] ?? 'active';
    $initial_credits = intval($postData['initial_credits'] ?? 10);
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt_insert = $conn->prepare("INSERT INTO users (username, email, password, first_name, last_name, phone, user_type, status, credits, email_verified, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())");
    $stmt_insert->bind_param('ssssssssi', $username, $email, $password_hash, $first_name, $last_name, $phone, $user_type, $status, $initial_credits);
    
    if ($stmt_insert->execute()) {
        $user_id = $conn->insert_id;
        require_once 'php/CreditManager.php';
        $creditManager = new CreditManager();
        $creditManager->addCredits($user_id, $initial_credits, 'admin_allocation', 'Initial credits allocated by admin when creating account');
        send_json_response(true, 'User successfully added.');
    } else {
        send_json_response(false, 'Failed to add user: ' . $stmt_insert->error);
    }
}

function handleUpdateCredits($conn, $postData) {
    $user_id = intval($postData['user_id'] ?? 0);
    $credits_action = $postData['credits_action'] ?? '';
    $credits_amount = intval($postData['credits_amount'] ?? 0);
    $credits_reason = trim($postData['credits_reason'] ?? '');

    if ($user_id <= 0 || $credits_amount <= 0 || empty($credits_reason) || !in_array($credits_action, ['add', 'deduct'])) {
        send_json_response(false, 'Invalid credit operation parameters.');
    }

    require_once 'php/CreditManager.php';
    $creditManager = new CreditManager();
    $result = false;
    $message = '';

    if ($credits_action === 'add') {
        $result = $creditManager->addCredits($user_id, $credits_amount, 'admin_allocation', $credits_reason);
        $message = $result ? 'Credits successfully added.' : 'Failed to add credits.';
    } else { // 'deduct'
        $result = $creditManager->deductCredits($user_id, $credits_amount, 'admin_deduction', $credits_reason);
        $message = $result ? 'Credits successfully deducted.' : 'Failed to deduct credits or insufficient balance.';
    }

    if ($result) {
        // Get updated credits count
        $stmt = $conn->prepare("SELECT credits FROM users WHERE id = ?");
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result_credits = $stmt->get_result();
        $user_credits = $result_credits->fetch_assoc();

        send_json_response(true, $message, ['new_credits' => $user_credits['credits'] ?? 0]);
    } else {
        send_json_response(false, $message);
    }
}

function handleExtendTrial($conn, $postData) {
    $user_id = intval($postData['user_id'] ?? 0);
    $extension_days = intval($postData['extension_days'] ?? 0);
    $reason = trim($postData['reason'] ?? '');
    $admin_id = $_SESSION['user_id'];

    if ($user_id <= 0 || $extension_days <= 0 || $extension_days > 30 || empty($reason)) {
        send_json_response(false, 'Invalid extension parameters. Maximum 30 days per extension.');
    }

    try {
        require_once 'php/TrialManager.php';
        $trialManager = new TrialManager();

        $result = $trialManager->extendTrialPeriod($user_id, $extension_days, $admin_id, $reason);

        if ($result['success']) {
            send_json_response(true, 'Trial period successfully extended.');
        } else {
            send_json_response(false, $result['message']);
        }
    } catch (Exception $e) {
        send_json_response(false, 'Failed to extend trial: ' . $e->getMessage());
    }
}