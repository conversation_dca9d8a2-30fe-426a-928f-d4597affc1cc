<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSRF Fix Test</title>
    <meta name="csrf-token" content="test-token-123">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        #results {
            margin-top: 20px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 3px;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>🔧 CSRF Token Fix Test</h1>
    
    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results">
            <p>Click the buttons below to test the fixes...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>Function Tests</h3>
        <button class="btn-primary" onclick="testCSRFToken()">Test CSRF Token</button>
        <button class="btn-success" onclick="testFormSubmission()">Test Form Submission</button>
        <button class="btn-info" onclick="testModalFocus()">Test Modal Focus Fix</button>
    </div>

    <div class="test-section">
        <h3>API Endpoint Tests</h3>
        <button class="btn-primary" onclick="testTrialStats()">Test Trial Stats</button>
        <button class="btn-success" onclick="testTrialSettings()">Test Trial Settings</button>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content" style="background: white; padding: 20px; border-radius: 5px;">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" onclick="closeModal()">×</button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal to verify focus handling.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="button" class="btn-primary" onclick="closeModal()">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : '';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function testCSRFToken() {
            log('Testing CSRF Token retrieval...');
            const token = document.querySelector('meta[name="csrf-token"]');
            if (token && token.getAttribute('content')) {
                log('✓ CSRF Token found: ' + token.getAttribute('content'), 'success');
            } else {
                log('✗ CSRF Token not found', 'error');
            }
        }

        function testFormSubmission() {
            log('Testing form submission logic...');
            
            // Create a test form
            const form = document.createElement('form');
            const actionInput = document.createElement('input');
            actionInput.name = 'action';
            actionInput.value = 'extend_trial';
            form.appendChild(actionInput);
            
            const formData = new FormData(form);
            const action = formData.get('action');
            
            // Test endpoint determination
            let apiEndpoint = 'admin_api.php';
            if (action === 'extend_trial' || action === 'update_trial_settings') {
                apiEndpoint = 'php/admin_trial_management.php';
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                formData.append('csrf_token', csrfToken);
            }
            
            log('✓ Action: ' + action, 'success');
            log('✓ Endpoint: ' + apiEndpoint, 'success');
            log('✓ CSRF Token added for trial actions', 'success');
        }

        function testModalFocus() {
            log('Testing modal focus handling...');
            
            const modal = document.getElementById('testModal');
            modal.style.display = 'block';
            
            // Simulate focus on a button
            const button = modal.querySelector('.btn-primary');
            button.focus();
            
            setTimeout(() => {
                // Test focus removal
                const focusedElement = modal.querySelector(':focus');
                if (focusedElement) {
                    focusedElement.blur();
                    log('✓ Focus removed from modal element', 'success');
                } else {
                    log('✓ No focused elements in modal', 'success');
                }
                
                modal.style.display = 'none';
            }, 1000);
        }

        function testTrialStats() {
            log('Testing trial stats API call...');
            
            const formData = new URLSearchParams();
            formData.append('action', 'get_trial_stats');
            
            log('✓ FormData created for trial stats', 'success');
            log('✓ No CSRF token needed for read-only operation', 'success');
        }

        function testTrialSettings() {
            log('Testing trial settings API call...');
            
            const formData = new URLSearchParams();
            formData.append('action', 'get_trial_settings');
            
            log('✓ FormData created for trial settings', 'success');
            log('✓ No CSRF token needed for read-only operation', 'success');
        }

        function closeModal() {
            document.getElementById('testModal').style.display = 'none';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 CSRF Fix Test Page Loaded');
            log('Ready to test the fixes for:');
            log('- CSRF Token handling');
            log('- Form submission routing');
            log('- Modal focus issues');
            log('- API endpoint calls');
        });
    </script>
</body>
</html>
