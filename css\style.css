/* KMS PC Receipt Maker - 優化後的主樣式文件 */

/* Unicode Icon Symbols - Replacing FontAwesome */
.icon-symbol {
    font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    display: inline-block;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-symbol.large {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.icon-symbol.extra-large {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

/* 1. 全局與根樣式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --transition: all 0.15s ease-in-out;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #1b8e93;
    background: #0095ff;
    min-height: 100vh;
    position: relative;
}

body.modal-open {
    overflow: hidden !important;
}

body::before {
    content: '';
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
    z-index: -1;
}

/* 自定義滾動條 */
::-webkit-scrollbar { width: 8px; }
::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px; }
::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 4px; }
::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }


/* 2. 佈局樣式 */
.kms-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 32px;
    min-height: 100vh;
}

.kms-main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 32px 0;
}

.kms-content-wrapper {
    background: rgb(91, 139, 237);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.container { animation: slideInUp 0.6s ease-out; }
.section { display: none; animation: fadeIn 0.3s ease-in-out; }
.section.active { display: block !important; }


/* 3. 導覽列 (Navbar) */
.kms-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.kms-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 12px 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-height: 80px;
}

.kms-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    font-weight: 700;
    font-size: 24px;
    text-decoration: none;
}

.kms-brand-icon {
    font-size: 32px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.kms-brand-text {
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.kms-nav-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
    position: relative;
    max-height: 70px;
    justify-content: center;
    max-width: calc(100% - 480px);
    margin: 0 auto;
}

.kms-nav-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
    height: 32px;
}

.kms-nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 12px;
    min-width: 100px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.kms-nav-btn::before {
    content: '';
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.kms-nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.kms-nav-btn:hover::before { left: 100%; }

.kms-nav-btn.active {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #ffffff;
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.kms-nav-btn.active:hover {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
}

/* History 按鈕特殊樣式 - 保持金黃色 */
.kms-nav-btn[data-action="history"] {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important;
    color: #ffffff !important;
    border-color: #ffd700 !important;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4) !important;
}

.kms-user-info {
    position: absolute;
    right: -95px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    z-index: 15;
    min-width: 200px;
    max-width: 240px;
    pointer-events: none;
}

.kms-user-welcome {
    color: white !important;
    font-weight: 600;
    font-size: 14px;
    pointer-events: auto;
}

.kms-user-credits {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important;
    color: #ffffff !important;
    padding: 4px 12px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    border: 2px solid #ffd700;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    pointer-events: auto;
}

.kms-language-group {
    display: flex;
    gap: 8px;
    margin-left: 16px;
    padding-left: 16px;
    border-left: 2px solid rgba(255, 255, 255, 0.2);
}

.kms-lang-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-lang-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.kms-lang-btn.active {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    border-color: rgba(255, 255, 255, 0.5);
}

.kms-mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.kms-mobile-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}


/* 4. 通用元件 (Components) */

/* 卡片 (Card) */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: #ff6e00;
    border: none;
    border-radius: 15px 15px 0 0 !important;
    padding: 4px 10px;
}

.card-header h5, .card-header h6 { margin: 0; font-weight: 600; }
.card-title { font-weight: 600; color: #333; }
.card-body { padding: 4px; background-color: #ffb400; }

/* 表單 (Form) */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 4px 6px;
    margin-bottom: 12px;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3.2px rgba(102, 126, 234, 0.25);
    background: white;
    transform: translateY(-1px);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

/* 按鈕 (Button) */
.btn {
    border-radius: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    padding: 12px 24px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
.btn:hover::before { left: 100%; }

.btn-primary { background: #0095ff; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); }
.btn-success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); box-shadow: 0 4px 15px rgba(17, 153, 142, 0.4); }
.btn-danger { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4); }
.btn-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4); color: white; }
.btn-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4); color: white; }
.btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4); }

.btn-lg { font-size: 14px; border-radius: 50px; }
.btn-xs { padding: 2px 4px; font-size: 12px; line-height: 1.2; border-radius: 3.2px; min-width: 28px; height: 24px; }
.btn-outline-danger:hover, .btn-outline-info:hover, .btn-outline-warning:hover { transform: scale(1.05); }
.btn-outline-danger:hover { box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3); }

.btn-group .btn { margin: 0 1px; }
.btn-group-sm .btn { padding: 4px 8px; font-size: 12.8px; }
.btn-group-vertical .btn-xs { margin-bottom: 2px; }
.btn-group-vertical .btn-xs:last-child { margin-bottom: 0; }

/* 徽章 (Badge) */
.badge {
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

/* 訊息提示 (Alert) */
.alert {
    border-radius: 15px;
    border: none;
    font-weight: 500;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}
.alert-success { background: linear-gradient(135deg, #d1edff 0%, #a7d8f0 100%); color: #0c5460; }
.alert-danger { background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%); color: #721c24; }
.alert-warning { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); color: #856404; }
.alert-info { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); color: #0c5460; }

/* 模態框 (Modal) */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1055 !important;
    width: 100% !important;
    height: 100% !important;
    overflow: hidden !important;
    outline: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    margin: 1.75rem auto;
    transform: none;
}
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100vh - 1rem);
}
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    background: rgb(87, 159, 236);
}
.modal-header { border-bottom: 1px solid rgba(0, 0, 0, 0.1); border-radius: 20px 20px 0 0; padding: 8px; }
.modal-body { padding: 8px; max-height: 80vh; overflow-y: auto; }
.modal-footer { border-top: 1px solid rgba(0, 0, 0, 0.1); border-radius: 0 0 20px 20px; padding: 8px; }

/* 模態框背景 */
.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1040 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 工具提示 (Tooltip) */
.tooltip { font-size: 14px; }

/* 載入狀態 (Loading) */
.loading {
    display: inline-block;
    width: 20px; height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}
.loading-overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.loading-spinner {
    width: 50px; height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}


/* 5. 特定區域/元件樣式 */

/* 通用互動項目基礎樣式 (合併重複樣式) */
.kms-interactive-item,
.receipt-item-row,
.preset-item,
.configuration-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    border-radius: 15px;
    transition: all 0.3s ease;
}
.kms-interactive-item:hover,
.receipt-item-row:hover,
.preset-item:hover,
.configuration-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* 項目列表 */
.item-row {
    padding: 16px;
    margin-bottom: 16px;
    border-color: #e9ecef; /* 恢復原有的邊框色 */
}
.item-row .btn-group { flex-wrap: nowrap; }
.item-row .btn-group .btn { padding: 4px 8px; font-size: 14px; border-radius: 8px; margin: 0 2px; }

/* 預設項目 */
.preset-item {
    height: 60px;
    display: flex;
    align-items: center;
}
#presetList { background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); border-radius: 10px; padding: 16px; }
.preset-item .btn-group { flex-wrap: wrap; gap: 4px; }
.preset-item .btn-group .btn { margin: 2px; }

/* 舊版收據項目 */
.legacy-receipt-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    transition: var(--transition);
}
.legacy-receipt-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.15);
}
.legacy-receipt-item .remove-item {
    position: absolute;
    top: 8px; right: 8px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px; height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}
.legacy-receipt-item .remove-item:hover { background: #bb2d3b; transform: scale(1.1); }

/* 預覽區域 */
.receipt-preview {
    background: #00ccff;
    border: 2px solid #ffffff82;
    border-radius: 15px;
    padding: 32px;
    min-height: 400px;
    transition: all 0.3s ease;
}
.receipt-preview.has-content {
    border-style: solid;
    border-color: #667eea;
    background: white;
}

/* 歷史記錄 */
.receipt-history-item {
    background-color: #ffb983;
    border: 1px solid #ffcd77bb;
    border-radius: 12px;
    margin-bottom: 2px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}
.receipt-history-item:hover {
    background-color: #00ffc3;
    border-color: #0dfdfd !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
.receipt-history-item .btn { transition: all 0.2s ease-in-out; }
.receipt-history-item .btn:hover { transform: scale(1.05); }
.receipt-number { font-weight: 600; color: var(--primary-color); }
.receipt-amount { font-weight: 600; font-size: 17.6px; color: var(--success-color); }

/* 拖拽排序 */
.order-controls { display: flex; flex-direction: column; align-items: center; gap: 8px; }
.order-input { width: 50px; text-align: center; font-size: 12.8px; }
.drag-handle { cursor: grab; color: #6c757d; padding: 4px; border-radius: 4px; transition: all 0.3s ease; }
.drag-handle:hover { color: #667eea; background-color: rgba(102, 126, 234, 0.1); }
.drag-handle:active { cursor: grabbing; }
.preset-item[draggable="true"] { cursor: move; }
.preset-item.dragging { opacity: 0.5; transform: rotate(2deg); }
.preset-item.drag-over { border-top: 3px solid #667eea !important; transform: translateY(-2px); }

/* Logo 預覽 */
.logo-preview { animation: slideInUp 0.3s ease-out; }
.logo-preview .card { border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); border-radius: 10px; }
.logo-preview .card-header { background: #fcca03; color: white; border-radius: 10px 10px 0 0; }
#logoPreviewImage { border: 2px solid #e9ecef; border-radius: 8px; transition: all 0.3s ease; }
#logoPreviewImage:hover { border-color: #667eea; transform: scale(1.05); }

/* 特定模態框樣式 */
#presetModal .modal-dialog { min-width: 75%; min-height: 75%; }
#presetModal .modal-content { min-height: 75vh; background: linear-gradient(135deg, #20b2aa 0%, #008b8b 100%); }
#receiptDetailsModal .modal-dialog { max-width: 90%; }
#receiptDetailsModal .card { border: 1px solid #dee2e6; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.075); }
#receiptDetailsModal .card-header { background-color: #f8f9fa; border-bottom: 1px solid #dee2e6; font-weight: 600; }
#receiptDetailsModal .table { margin-bottom: 0; }
#receiptDetailsModal .table th { background-color: #f8f9fa; border-top: none; font-weight: 600; font-size: 14px; }
#receiptDetailsModal .table td { vertical-align: middle; font-size: 14px; }
#receiptDetailsModal .badge { font-size: 12px; }
#receiptDetailsModal .modal-body { max-height: 70vh; overflow-y: auto; }

/* 總計顯示 */
#totalsDisplay .card { border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); border-radius: 15px; }
#totalsDisplay .card-body { padding: 24px; }
#totalsDisplay .row { margin-bottom: 8px; align-items: center; }
#totalsDisplay hr { margin: 16px 0; border-top: 2px solid #667eea; }

/* 批次刪除 */
.batch-controls {
    border: 1px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    animation: slideDown 0.3s ease-out;
}
.batch-controls .btn { transition: all 0.2s ease-in-out; }
.batch-controls .btn:hover { transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
.receipt-checkbox { transform: scale(1.2); margin-right: 8px; }
.receipt-checkbox:checked { background-color: #0d6efd; border-color: #0d6efd; }
.receipt-history-item:has(.receipt-checkbox:checked) {
    background-color: #e7f3ff;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.1);
}

/* Customer Info 區域樣式 */
.customer-info-section {
    background: #1cc1dc;
    border-radius: 15px;
    padding: 24px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}
.customer-info-section h3 {
    font-weight: 600;
    margin-bottom: 20px;
    color: #ffffff;
}
.customer-info-section .form-label {
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
}
.customer-info-section .form-control {
    border-width: 2px;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 16px;
    background-color: #fff;
}
.customer-info-section .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
}
.customer-info-section .form-control::placeholder {
    color: #adb5bd;
    font-style: italic;
}


/* 6. 動畫 (Animations) */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
@keyframes slideInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes slideInDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}
@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}


/* 7. 響應式設計 (Responsive Design) */
@media (max-width: 768px) {
    .container { padding-left: 16px; padding-right: 16px; }
    .kms-nav-container { padding: 16px; }
    .kms-nav-menu {
        display: none;
        position: absolute;
        top: 100%; left: 0; right: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 16px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        align-items: stretch;
    }
    .kms-nav-menu.active { display: flex; }
    .kms-mobile-toggle { display: flex; }

    .kms-nav-row {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 12px;
    }

    .kms-nav-btn {
        min-width: auto;
        width: 100%;
        justify-content: flex-start;
    }
    .kms-brand-text { font-size: 19.2px; }
    .card-body { padding: 16px; }
    .btn { margin-bottom: 8px; }
    .d-flex.gap-2.flex-wrap .btn { flex: 1 1 auto; min-width: 120px; }
    .legacy-receipt-item { padding: 12px; }
    .navbar-brand { font-size: 16px; }
    .form-group.mb-0 { margin-bottom: 8px !important; }
    .d-flex.gap-2.align-items-center.flex-wrap {
        flex-direction: column;
        align-items: stretch !important;
        gap: 8px !important;
    }
    .d-flex.gap-2.align-items-center.flex-wrap > * { margin-bottom: 8px; }
    .batch-controls { flex-direction: column; gap: 8px; }
    .batch-controls .d-flex { flex-direction: column; align-items: stretch !important; gap: 8px; }
    .receipt-history-item .col-md-1,
    .receipt-history-item .col-md-2 { flex: 0 0 auto; width: auto; }
    
    /* 用戶管理響應式 */
    .kms-user-info {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        flex-direction: column;
        gap: 4px;
        margin-bottom: 12px;
        text-align: center;
        align-items: center;
    }

    .quick-nav .nav-text {
        display: none;
    }

    .fixed-logout-btn {
        padding: 12px;
        bottom: 15px;
        right: 15px;
    }
    .fixed-logout-btn .logout-text {
        display: none;
    }
    
    .user-welcome {
        font-size: 0.8rem;
    }
    
    .user-type-badge {
        font-size: 0.7rem;
        padding: 0.15rem 0.5rem;
    }
    
    .admin-btn,
    .profile-btn,
    .logout-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    /* 折扣計算響應式 */
    .totals-summary {
        min-width: 200px;
        padding: 0.75rem;
    }
    
    .total-line {
        font-size: 0.85rem;
        padding: 0.4rem 0;
    }
    
    .total-final {
        font-size: 1rem;
    }
    
    .discount-input,
    .tax-input {
        width: 60px !important;
    }
    
    #discountType {
        width: 80px !important;
    }
    
    .form-group.mb-0 {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .form-group.mb-0 .form-label {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .container { padding-left: 8px; padding-right: 8px; }
    .card { margin-bottom: 16px; }
    .receipt-preview { min-height: 300px; padding: 16px; }
}


/* 8. 用戶管理樣式 */
.kms-user-info-old {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-welcome {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.user-type-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-type-badge.admin {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
}

.user-type-badge.member {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
}

.user-credits-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-weight: 600;
    /* 警告: --color-3, --color-5, --text-color-2, --border-color-3 未定義。已替換為相似變數 */
    background: linear-gradient(45deg, var(--warning-color), var(--info-color));
    color: var(--dark-color);
    border: 1px solid var(--warning-color);
    display: flex;
    align-items: center;
    gap: 0.3rem;
    box-shadow: 0 2px 8px rgba(0, 255, 128, 0.2);
    transition: all 0.3s ease;
}

.user-credits-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 128, 0.3);
}

.user-credits-badge .icon-symbol {
    font-size: 0.8rem;
}

.credits-history-btn {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    border: 1px solid #ffd700;
    color: #ffffff;
    padding: 0.375rem 0.5rem;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    min-width: auto;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credits-history-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
    color: #ffffff;
}
.credits-history-btn:active { transform: translateY(0); }
.credits-history-btn .icon-symbol { font-size: 1rem; }

.admin-btn, .profile-btn, .delete-account-btn, .logout-btn {
    color: white;
    border: none;
}
.admin-btn { background: linear-gradient(45deg, #ffed4e, #ffd700); }
.profile-btn { background: linear-gradient(45deg, #ffed4e, #ffd700) }
.delete-account-btn { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }
.logout-btn { background: linear-gradient(45deg, #ffed4e, #ffd700) }

.admin-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    box-shadow: 0 4px 15px rgba(253, 121, 168, 0.3);
}
.profile-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}
.delete-account-btn:hover {
    background: linear-gradient(135deg, #ee5a24 0%, #d63031 100%);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    transform: translateY(-2px);
}
.logout-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    box-shadow: 0 4px 15px rgba(255, 118, 117, 0.3);
}

.quick-nav a, .quick-nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 16px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

/* 聯繫方式按鈕樣式 */
.contact-buttons {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.contact-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateX(-5px);
    color: white;
    text-decoration: none;
}

.email-btn:hover {
    background: rgba(52, 152, 219, 0.3);
    border-color: #3498db;
}

.phone-btn:hover {
    background: rgba(46, 204, 113, 0.3);
    border-color: #2ecc71;
}

.contact-text {
    font-size: 12px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.quick-nav a:hover, .quick-nav-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateX(-5px);
}
.quick-nav .nav-text { white-space: nowrap; }
.quick-nav-actions { margin-top: 16px; padding-top: 16px; border-top: 1px solid rgba(255, 255, 255, 0.2); }

.quick-nav-btn.btn-save:hover { background: rgba(40, 167, 69, 0.3); border-color: #28a745; }
.quick-nav-btn.btn-print:hover { background: rgba(23, 162, 184, 0.3); border-color: #17a2b8; }

.fixed-logout-btn {
    position: fixed;
    bottom: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
}

.fixed-logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.6);
}


/* 9. 折扣計算和總計顯示樣式 */
.totals-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem;
    border: 2px solid #dee2e6;
    min-width: 250px;
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.95rem;
}

.total-line:last-child {
    border-bottom: none;
}

.total-final {
    border-top: 2px solid #007bff;
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    font-size: 1.1rem;
    color: #007bff;
}

.discount-line {
    color: #28a745;
    font-weight: 600;
}

.discount-input,
.tax-input {
    width: 80px !important;
    margin: 0 0.5rem;
}

#discountType {
    width: 100px !important;
    margin-right: 0.5rem;
}

#discountUnit {
    margin-left: 0.25rem;
    font-weight: 600;
    color: #6c757d;
}

.form-group.mb-0 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group.mb-0 .form-label {
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: 600;
    color: #495057;
}

.form-control-sm.d-inline-block {
    width: auto;
    display: inline-block !important;
}

/* 代理會話樣式 */
.proxy-session-indicator {
    background: linear-gradient(135deg, #ff6b6b, #ffa500);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
    animation: pulse-glow 2s infinite;
}

.proxy-session-indicator small {
    font-size: 0.75rem;
    opacity: 0.9;
    font-weight: 400;
}

.return-admin-btn {
    background: linear-gradient(135deg, #ff6b6b, #ff8e53) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    transition: all 0.3s ease;
}

.return-admin-btn:hover {
    background: linear-gradient(135deg, #ff5252, #ff7043) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.return-admin-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 2px 20px rgba(255, 107, 107, 0.6);
    }
}

/* 10. 列印樣式 (Print) */
@media print {
    /* 隱藏非列印內容 */
    .kms-navbar, .kms-user-group, .fixed-logout-btn,
    #receiptDetailsModal .modal-header,
    #receiptDetailsModal .modal-footer {
        display: none !important;
    }

    #receiptDetailsModal .modal-body {
        max-height: none !important;
        overflow: visible !important;
    }
    #receiptDetailsModal .card {
        break-inside: avoid;
        margin-bottom: 16px;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* 響應式設計 - 聯繫方式按鈕 */
@media (max-width: 768px) {
    .contact-btn {
        padding: 10px 12px;
        font-size: 13px;
    }

    .contact-text {
        font-size: 11px;
    }

    .proxy-session-indicator {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
    }
}