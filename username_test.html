<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>註冊 - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/auth.css" rel="stylesheet">
    <link href="css/form-validation.css" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card auth-card-wide">
            <div class="auth-header">
                <div class="auth-logo">
                    <span class="auth-icon">🧾</span>
                    <h1 class="auth-title">KMS Receipt Maker</h1>
                </div>
                <h2 class="auth-subtitle" data-lang="register_title">User Registration</h2>
            </div>
            
                        
                            <div class="alert alert-danger" role="alert">
                                            <div>Username must be between 6 and 20 characters</div>
                                    </div>
                        
                        <form method="POST" class="auth-form">
                <!-- Username -->
                <div class="form-group">
                    <label for="username" class="form-label" data-lang="username">Username *</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="test" required>
                </div>
                
                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label" data-lang="email">Email Address *</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<EMAIL>" required>
                </div>
                
                <!-- First Name and Last Name -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label" data-lang="first_name">First Name *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="Test" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="form-label" data-lang="last_name">Last Name *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="User" required>
                        </div>
                    </div>
                </div>
                
                <!-- Phone Number -->
                <div class="form-group">
                    <label for="phone" class="form-label" data-lang="phone">Phone Number</label>
                    <input type="tel" class="form-control" id="phone" name="phone" 
                           value="">
                </div>
                
                <!-- Password -->
                <div class="form-group">
                    <label for="password" class="form-label" data-lang="password">Password *</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    <div class="password-requirements" id="passwordRequirements">
                        <small class="text-muted" data-lang="password_requirements">密碼要求：</small>
                        <ul class="password-rules">
                            <li id="rule-length" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_length">至少8個字符</span></li>
                            <li id="rule-uppercase" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_uppercase">包含一個大寫字母</span></li>
                            <li id="rule-number" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_number">包含一個數字</span></li>
                            <li id="rule-special" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_special">包含一個特殊符號 (!@#$%^&*)</span></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Confirm Password -->
                <div class="form-group">
                    <label for="confirm_password" class="form-label" data-lang="confirm_password">Confirm Password *</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    <div class="password-match" id="passwordMatch" style="display: none;">
                        <small class="match-message"></small>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary auth-btn" data-lang="register_btn">Register</button>
            </form>
                        
            <div class="auth-links">
                <a href="login.php" class="auth-link" data-lang="login_link">Already have an account? Login now</a>
            </div>
            
            <div class="language-switcher">
                <button class="kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                    <span class="flag-icon">🌐</span> English
                </button>
                <button class="kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                    <span class="flag-icon">🌐</span> 中文
                </button>
            </div>
        </div>
    </div>
    
    <script src="js/language.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/form-validation.js"></script>
</body>
</html>