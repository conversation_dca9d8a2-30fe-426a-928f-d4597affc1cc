<?php
/**
 * 管理員試用管理處理器
 * KMS Receipt Maker - Admin Trial Management Handler
 */

// 禁用錯誤顯示，防止破壞 JSON 輸出
error_reporting(0);
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
ini_set('log_errors', 1);
ini_set('html_errors', 0);

// 設置內容類型為 JSON
header('Content-Type: application/json; charset=utf-8');

// 開始輸出緩衝
ob_start();

session_start();

require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';
require_once 'TrialManager.php';

// 檢查管理員權限
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    Response::error('Admin privileges required');
    exit;
}

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('Method not allowed');
    exit;
}

// CSRF Token 驗證（除了 get_trial_stats 等讀取操作）
$readOnlyActions = ['get_trial_settings', 'get_trial_stats', 'get_expiring_users', 'get_trial_users_list'];
$action = $_POST['action'] ?? '';

if (!in_array($action, $readOnlyActions)) {
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) ||
        !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        Response::error('Invalid security token. Please refresh the page and try again.');
        exit;
    }
}

$action = $_POST['action'] ?? '';
$adminId = $_SESSION['user_id'];

try {
    $trialManager = new TrialManager();
    
    switch ($action) {
        case 'get_trial_settings':
            // 獲取試用系統設置
            $settings = [
                'default_trial_days' => $trialManager->getTrialSetting('default_trial_days', 14),
                'enable_trial_system' => $trialManager->getTrialSetting('enable_trial_system', 1),
                'max_trial_extensions' => $trialManager->getTrialSetting('max_trial_extensions', 3),
                'max_extension_days' => $trialManager->getTrialSetting('max_extension_days', 30),
                'auto_disable_expired_trials' => $trialManager->getTrialSetting('auto_disable_expired_trials', 1)
            ];
            
            Response::success($settings, 'Trial settings retrieved');
            break;
            
        case 'update_trial_settings':
            $defaultTrialDays = intval($_POST['default_trial_days'] ?? 14);
            $enableTrialSystem = intval($_POST['enable_trial_system'] ?? 1);
            $maxTrialExtensions = intval($_POST['max_trial_extensions'] ?? 3);
            $maxExtensionDays = intval($_POST['max_extension_days'] ?? 30);
            $autoDisableExpiredTrials = intval($_POST['auto_disable_expired_trials'] ?? 1);
            
            // 驗證輸入
            if ($defaultTrialDays < 1 || $defaultTrialDays > 365) {
                Response::error('Default trial days must be between 1 and 365');
                exit;
            }
            
            if ($maxExtensionDays < 1 || $maxExtensionDays > 90) {
                Response::error('Max extension days must be between 1 and 90');
                exit;
            }
            
            // 更新設置
            $trialManager->updateTrialSetting('default_trial_days', $defaultTrialDays, 'Default trial period in days for new users');
            $trialManager->updateTrialSetting('enable_trial_system', $enableTrialSystem, 'Enable trial system for new registrations');
            $trialManager->updateTrialSetting('max_trial_extensions', $maxTrialExtensions, 'Maximum number of trial extensions per user');
            $trialManager->updateTrialSetting('max_extension_days', $maxExtensionDays, 'Maximum days that can be extended at once');
            $trialManager->updateTrialSetting('auto_disable_expired_trials', $autoDisableExpiredTrials, 'Automatically disable expired trial accounts');
            
            Response::success([], 'Trial settings updated successfully');
            break;
            
        case 'extend_user_trial':
            $userId = intval($_POST['user_id'] ?? 0);
            $extensionDays = intval($_POST['extension_days'] ?? 0);
            $reason = $_POST['reason'] ?? '';
            
            if ($userId <= 0) {
                Response::error('Invalid user ID');
                exit;
            }
            
            if ($extensionDays <= 0) {
                Response::error('Extension days must be greater than 0');
                exit;
            }
            
            $result = $trialManager->extendTrialPeriod($userId, $extensionDays, $adminId, $reason);
            
            if ($result['success']) {
                Response::success($result, $result['message']);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'convert_trial_to_paid':
            $userId = intval($_POST['user_id'] ?? 0);
            
            if ($userId <= 0) {
                Response::error('Invalid user ID');
                exit;
            }
            
            $result = $trialManager->convertTrialToPaid($userId, $adminId);
            
            if ($result['success']) {
                Response::success($result, $result['message']);
            } else {
                Response::error($result['message']);
            }
            break;
            
        case 'get_trial_stats':
            $stats = $trialManager->getTrialStats();
            Response::success($stats, 'Trial statistics retrieved');
            break;
            
        case 'get_expiring_users':
            $limit = intval($_POST['limit'] ?? 50);
            $expiringUsers = $trialManager->getExpiringTrialUsers($limit);
            Response::success($expiringUsers, 'Expiring trial users retrieved');
            break;
            
        case 'get_user_trial_extensions':
            $userId = intval($_POST['user_id'] ?? 0);
            
            if ($userId <= 0) {
                Response::error('Invalid user ID');
                exit;
            }
            
            $extensions = $trialManager->getUserTrialExtensions($userId);
            Response::success($extensions, 'User trial extensions retrieved');
            break;
            
        case 'get_trial_users_list':
            $db = new Database();
            $conn = $db->getConnection();
            
            $page = intval($_POST['page'] ?? 1);
            $limit = intval($_POST['limit'] ?? 20);
            $offset = ($page - 1) * $limit;
            
            $statusFilter = $_POST['status_filter'] ?? 'all';
            $searchTerm = $_POST['search_term'] ?? '';
            
            // 構建查詢條件
            $whereConditions = ['is_trial_user = 1'];
            $params = [];
            $paramTypes = '';
            
            if ($statusFilter !== 'all') {
                $whereConditions[] = 'trial_status = ?';
                $params[] = $statusFilter;
                $paramTypes .= 's';
            }
            
            if (!empty($searchTerm)) {
                $whereConditions[] = '(username LIKE ? OR email LIKE ?)';
                $params[] = "%{$searchTerm}%";
                $params[] = "%{$searchTerm}%";
                $paramTypes .= 'ss';
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // 獲取總數
            $countSql = "SELECT COUNT(*) as total FROM users WHERE {$whereClause}";
            $countStmt = $conn->prepare($countSql);
            if (!empty($params)) {
                $countStmt->bind_param($paramTypes, ...$params);
            }
            $countStmt->execute();
            $totalCount = $countStmt->get_result()->fetch_assoc()['total'];
            
            // 獲取用戶列表
            $sql = "
                SELECT 
                    id, username, email, trial_start_date, trial_end_date, 
                    trial_days, trial_extended_days, trial_status,
                    DATEDIFF(trial_end_date, NOW()) as days_remaining,
                    created_at
                FROM users 
                WHERE {$whereClause}
                ORDER BY trial_end_date ASC
                LIMIT ? OFFSET ?
            ";
            
            $stmt = $conn->prepare($sql);
            $params[] = $limit;
            $params[] = $offset;
            $paramTypes .= 'ii';
            
            if (!empty($params)) {
                $stmt->bind_param($paramTypes, ...$params);
            }
            $stmt->execute();
            $result = $stmt->get_result();
            
            $users = [];
            while ($row = $result->fetch_assoc()) {
                $users[] = $row;
            }
            
            Response::success([
                'users' => $users,
                'total_count' => $totalCount,
                'current_page' => $page,
                'total_pages' => ceil($totalCount / $limit)
            ], 'Trial users list retrieved');
            break;

        case 'extend_trial':
            $user_id = intval($_POST['user_id'] ?? 0);
            $extension_days = intval($_POST['extension_days'] ?? 0);
            $reason = trim($_POST['reason'] ?? '');

            if ($user_id <= 0 || $extension_days <= 0 || $extension_days > 30 || empty($reason)) {
                Response::error('Invalid extension parameters. Maximum 30 days per extension.');
                break;
            }

            $result = $trialManager->extendTrialPeriod($user_id, $extension_days, $adminId, $reason);

            if ($result['success']) {
                Response::success(null, 'Trial period successfully extended.');
            } else {
                Response::error($result['message']);
            }
            break;

        default:
            Response::error('Invalid action');
            break;
    }
    
} catch (Exception $e) {
    // 清理輸出緩衝
    ob_clean();
    error_log("Admin trial management error: " . $e->getMessage());
    Response::error('System error occurred');
}

// 清理輸出緩衝並結束
ob_end_clean();
?>
