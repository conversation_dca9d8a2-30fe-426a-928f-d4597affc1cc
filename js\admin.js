/**
 * Admin Panel JavaScript Functions - admin.js (Optimized Version)
 *
 * Features:
 * - Uses AJAX for all backend operations without page refresh
 * - Uses event delegation (data-action) for unified click event management
 * - Unified API request handling through apiCall helper function with error handling and CSRF Token
 * - Debounce technology for user input optimization
 * - Uses async/await for cleaner asynchronous code
 */

// Debounce helper function
function debounce(func, delay = 300) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

document.addEventListener('DOMContentLoaded', () => {
    bindEventListeners();
    loadDashboardData();
});

/**
 * Load asynchronous dashboard data (such as trial statistics)
 */
function loadDashboardData() {
    loadTrialStats();
    // loadExpiringTrialUsers(); // Optional, if available
}

/**
 * Bind all event listeners uniformly
 */
function bindEventListeners() {
    // 1. Use event delegation to handle all button clicks
    document.body.addEventListener('click', (event) => {
        const actionTarget = event.target.closest('[data-action]');
        if (!actionTarget) return;

        const { action, userId, username, credits, daysRemaining } = actionTarget.dataset;
        const userData = actionTarget.dataset.userData ? JSON.parse(actionTarget.dataset.userData) : {};

        // Execute corresponding function based on data-action value
        switch (action) {
            case 'approve-user': handleUserAction('approve_user', userId, `Are you sure you want to approve user ${username}?`, 'Approve'); break;
            case 'reject-user':  handleUserAction('reject_user', userId, `Are you sure you want to reject user ${username}?`, 'Reject'); break;
            case 'delete-user':  handleUserAction('delete_user', userId, `Are you sure you want to permanently delete user ${username}? This action cannot be undone!`, 'Delete', 'btn-danger'); break;
            case 'edit-user':    openEditUserModal(userData); break;
            case 'open-add-user-modal': openAddUserModal(); break;
            case 'open-credits-modal': openCreditsModal(userId, credits, username); break;
            case 'proxy-login':  proxyLogin(userId, username); break;
            case 'return-to-admin': returnToAdmin(); break;
            // Trial related
            case 'open-trial-settings-modal': openTrialSettingsModal(); break;
            case 'extend-trial': openExtendTrialModal(userId, username, daysRemaining); break;
        }
    });

    // 2. Bind form submission events
    document.getElementById('editUserForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('addUserForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('creditsForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('extendTrialForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('trialSettingsForm')?.addEventListener('submit', handleFormSubmit);

    // 3. Bind search input events (with debounce)
    const searchInput = document.getElementById('userSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleUserSearch));
    }
}

// =====================================================
// Generic Helper Functions (API, Modal, Notifications)
// =====================================================

/**
 * Generic API request function
 * @param {FormData | URLSearchParams} body - Request body containing action and other data
 * @param {boolean} showLoader - Whether to show loading animation
 * @returns {Promise<object>} - Parsed JSON data
 */
async function apiCall(body, showLoader = true) {
    if (showLoader) showLoadingState();

    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    body.append('csrf_token', csrfToken);

    try {
        const response = await fetch('admin_api.php', { method: 'POST', body });
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `HTTP Error: ${response.status}`);
        }
        return data;
    } catch (error) {
        console.error('API Call Error:', error);
        showNotification(`Request failed: ${error.message}`, 'danger');
        throw error; // Re-throw error so caller can handle it
    } finally {
        if (showLoader) hideLoadingState();
    }
}

/**
 * Show a generic confirmation modal
 * @param {object} options - Configuration
 * @returns {Promise<boolean>} - resolve(true) if confirmed, resolve(false) otherwise
 */
function showConfirm({ title, message, confirmText = 'Confirm', confirmClass = 'btn-primary' }) {
    return new Promise((resolve) => {
        const template = document.getElementById('confirmModalTemplate');
        if (!template) return resolve(false);

        const modalEl = template.content.firstElementChild.cloneNode(true);
        document.body.appendChild(modalEl);

        modalEl.querySelector('.modal-title').textContent = title;
        modalEl.querySelector('.modal-body').innerHTML = `<p>${message}</p>`;
        const confirmBtn = modalEl.querySelector('.btn-confirm');
        confirmBtn.textContent = confirmText;
        confirmBtn.className = `btn btn-confirm ${confirmClass}`;

        const modal = new bootstrap.Modal(modalEl);

        const onConfirm = () => {
            resolve(true);
            modal.hide();
        };
        const onCancel = () => resolve(false);

        confirmBtn.addEventListener('click', onConfirm);
        modalEl.addEventListener('hidden.bs.modal', () => {
            onCancel();
            modalEl.remove();
        }, { once: true });

        modal.show();
    });
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.setAttribute('role', 'alert');
    notification.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>`;
    document.body.appendChild(notification);
    setTimeout(() => bootstrap.Alert.getOrCreateInstance(notification)?.close(), 5000);
}

/**
 * Show loading state
 */
function showLoadingState() {
    // Create loading overlay if it doesn't exist
    let loadingOverlay = document.getElementById('loadingOverlay');
    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loadingOverlay';
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;
        loadingOverlay.innerHTML = `
            <div style="background: white; padding: 2rem; border-radius: 10px; text-align: center;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div style="margin-top: 1rem;">Processing...</div>
            </div>
        `;
        document.body.appendChild(loadingOverlay);
    }
    loadingOverlay.style.display = 'flex';
}

/**
 * Hide loading state
 */
function hideLoadingState() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// =====================================================
// Event Handler Functions
// =====================================================

/**
 * Generic user action handler (approve, reject, delete)
 */
async function handleUserAction(action, userId, confirmMessage, confirmText, confirmClass) {
    const confirmed = await showConfirm({
        title: `Confirm ${confirmText}`,
        message: confirmMessage,
        confirmText: confirmText,
        confirmClass: confirmClass
    });

    if (!confirmed) return;

    const body = new URLSearchParams({ action, user_id: userId });
    try {
        const data = await apiCall(body);
        if (data.success) {
            showNotification(data.message, 'success');
            // Simply reload the page to update UI
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showNotification(data.message, 'danger');
        }
    } catch (error) {
        // apiCall already handles notifications internally
    }
}

/**
 * Generic form submission handler
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    const form = event.target;

    // Form validation logic can be added here

    const formData = new FormData(form);
    const action = formData.get('action'); // Ensure form has name="action" input

    try {
        // Determine API endpoint based on action
        let apiEndpoint = 'admin_api.php';
        if (action === 'extend_trial' || action === 'update_trial_settings') {
            apiEndpoint = 'php/admin_trial_management.php';
            // Add CSRF token for trial management endpoints
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            formData.append('csrf_token', csrfToken);
        }

        const response = await fetch(apiEndpoint, { method: 'POST', body: formData });
        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            const modalElement = form.closest('.modal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    // Remove focus from any buttons before hiding modal
                    const focusedElement = modalElement.querySelector(':focus');
                    if (focusedElement) {
                        focusedElement.blur();
                    }
                    modal.hide();
                }
            }
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showNotification(data.message, 'danger');
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification(`Request failed: ${error.message}`, 'danger');
    }
}

/**
 * User search functionality
 */
function handleUserSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('.admin-table tbody tr');

    tableRows.forEach(row => {
        const username = row.cells[1]?.textContent.toLowerCase() || '';
        const email = row.cells[2]?.textContent.toLowerCase() || '';
        row.style.display = (username.includes(searchTerm) || email.includes(searchTerm)) ? '' : 'none';
    });
}

// =====================================================
// Modal Opening and Data Population
// =====================================================

function openEditUserModal(userData) {
    const modalEl = document.getElementById('editUserModal');
    if (!modalEl) return;

    // 填充表單
    Object.keys(userData).forEach(key => {
        const input = modalEl.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = userData[key] || '';
        }
    });
    // 特殊處理 user_id
    modalEl.querySelector('#edit_user_id').value = userData.id;

    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

function openAddUserModal() {
    const modalEl = document.getElementById('addUserModal');
    if (!modalEl) return;
    modalEl.querySelector('form').reset();
    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

function openCreditsModal(userId, currentCredits, username) {
    const modalEl = document.getElementById('creditsModal');
    if (!modalEl) return;

    modalEl.querySelector('form').reset();
    document.getElementById('credits_user_id').value = userId;
    document.getElementById('credits_username').value = username;
    document.getElementById('credits_current').value = currentCredits;

    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

function openExtendTrialModal(userId, username, daysRemaining) {
    const modalEl = document.getElementById('extendTrialModal');
    if (!modalEl) return;

    modalEl.querySelector('form').reset();
    document.getElementById('extend_user_id').value = userId;
    document.getElementById('extend_username').value = username;
    document.getElementById('extend_current_days').value = daysRemaining + ' days';

    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

// =====================================================
// Proxy Login and Trial Management (These functions remain unchanged as they may call different API endpoints)
// =====================================================

async function proxyLogin(userId, username) {
    showLoadingState();
    try {
        const response = await fetch('php/admin_proxy_login.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'proxy_login', target_user_id: userId })
        });
        const data = await response.json();
        if (data.success) {
            showNotification(`Successfully logged in as ${username}`, 'success');
            setTimeout(() => window.location.href = data.data.redirect_url, 1500);
        } else {
            showNotification(data.message || 'Proxy login failed', 'danger');
        }
    } catch (error) {
        showNotification('Network error: ' + error.message, 'danger');
    } finally {
        hideLoadingState();
    }
}

async function returnToAdmin() {
    showLoadingState();
    try {
        const response = await fetch('php/admin_proxy_login.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'return_to_admin' })
        });
        const data = await response.json();
        if (data.success) {
            window.location.href = data.data.redirect_url;
        } else {
            showNotification(data.message || 'Return to admin failed', 'danger');
        }
    } catch (error) {
        showNotification('Network error: ' + error.message, 'danger');
    } finally {
        hideLoadingState();
    }
}

async function loadTrialStats() {
    try {
        const formData = new URLSearchParams();
        formData.append('action', 'get_trial_stats');

        const response = await fetch('php/admin_trial_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: formData
        });
        const data = await response.json();
        if (data.success) {
            const stats = data.data;
            document.getElementById('activeTrialsCount').textContent = stats.active_trials || 0;
            document.getElementById('expiringTrialsCount').textContent = stats.expiring_soon || 0;
            document.getElementById('expiredTrialsCount').textContent = stats.expired_trials || 0;
            document.getElementById('convertedTrialsCount').textContent = stats.converted_trials || 0;
        }
    } catch (error) {
        console.error('Error loading trial stats:', error);
    }
}

// Trial Settings Modal function
async function openTrialSettingsModal() {
    const modalEl = document.getElementById('trialSettingsModal');
    if (!modalEl) return;

    try {
        // Load current trial settings
        const formData = new URLSearchParams();
        formData.append('action', 'get_trial_settings');

        const response = await fetch('php/admin_trial_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: formData
        });
        const data = await response.json();

        if (data.success) {
            const settings = data.data;
            document.getElementById('default_trial_days').value = settings.default_trial_days || 14;
            document.getElementById('max_trial_extensions').value = settings.max_trial_extensions || 3;
            document.getElementById('max_extension_days').value = settings.max_extension_days || 30;
            document.getElementById('enable_trial_system').checked = settings.enable_trial_system == 1;
            document.getElementById('auto_disable_expired_trials').checked = settings.auto_disable_expired_trials == 1;
        }
    } catch (error) {
        console.error('Error loading trial settings:', error);
        showNotification('Failed to load trial settings', 'danger');
    }

    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}