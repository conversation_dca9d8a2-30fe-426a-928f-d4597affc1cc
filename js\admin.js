/**
 * 管理員面板 JavaScript 功能 - admin.js (優化版)
 *
 * 特性:
 * - 使用 AJAX 進行所有後端操作，無需刷新頁面。
 * - 使用事件委派 (`data-action`) 統一管理點擊事件，提升效能。
 * - 透過 `apiCall` 輔助函數統一處理 API 請求、錯誤和 CSRF Token。
 * - 對使用者輸入使用防抖 (Debounce) 技術優化體驗。
 * - 採用 async/await 讓非同步程式碼更清晰。
 */

// 防抖輔助函數
function debounce(func, delay = 300) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

document.addEventListener('DOMContentLoaded', () => {
    bindEventListeners();
    loadDashboardData();
});

/**
 * 載入非同步儀表板數據 (如試用統計)
 */
function loadDashboardData() {
    loadTrialStats();
    // loadExpiringTrialUsers(); // 可選，如果有的話
}

/**
 * 統一綁定所有事件監聽器
 */
function bindEventListeners() {
    // 1. 使用事件委派處理所有按鈕點擊
    document.body.addEventListener('click', (event) => {
        const actionTarget = event.target.closest('[data-action]');
        if (!actionTarget) return;

        const { action, userId, username, credits } = actionTarget.dataset;
        const userData = actionTarget.dataset.userData ? JSON.parse(actionTarget.dataset.userData) : {};

        // 根據 data-action 的值執行對應函數
        switch (action) {
            case 'approve-user': handleUserAction('approve_user', userId, `確定要批准用戶 ${username} 嗎？`, '批准'); break;
            case 'reject-user':  handleUserAction('reject_user', userId, `確定要拒絕用戶 ${username} 嗎？`, '拒絕'); break;
            case 'delete-user':  handleUserAction('delete_user', userId, `確定要永久刪除用戶 ${username} 嗎？此操作無法復原！`, '刪除', 'btn-danger'); break;
            case 'edit-user':    openEditUserModal(userData); break;
            case 'open-add-user-modal': openAddUserModal(); break;
            case 'open-credits-modal': openCreditsModal(userId, credits, username); break;
            case 'proxy-login':  proxyLogin(userId, username); break;
            case 'return-to-admin': returnToAdmin(); break;
            // 試用相關
            case 'open-trial-settings-modal': openTrialSettingsModal(); break;
        }
    });

    // 2. 綁定表單提交事件
    document.getElementById('editUserForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('addUserForm')?.addEventListener('submit', handleFormSubmit);
    document.getElementById('creditsForm')?.addEventListener('submit', handleFormSubmit);

    // 3. 綁定搜尋輸入事件 (含防抖)
    const searchInput = document.getElementById('userSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleUserSearch));
    }
}

// =====================================================
// 通用輔助函數 (API, Modal, 通知)
// =====================================================

/**
 * 通用的 API 請求函數
 * @param {FormData | URLSearchParams} body - 包含 action 和其他數據的請求體
 * @param {boolean} showLoader - 是否顯示載入動畫
 * @returns {Promise<object>} - 解析後的 JSON 數據
 */
async function apiCall(body, showLoader = true) {
    if (showLoader) showLoadingState();
    
    // 從 meta 標籤獲取 CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    body.append('csrf_token', csrfToken);

    try {
        const response = await fetch('admin_api.php', { method: 'POST', body });
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || `HTTP 錯誤: ${response.status}`);
        }
        return data;
    } catch (error) {
        console.error('API Call Error:', error);
        showNotification(`請求失敗: ${error.message}`, 'danger');
        throw error; // 向上拋出錯誤，讓呼叫者可以處理
    } finally {
        if (showLoader) hideLoadingState();
    }
}

/**
 * 顯示一個通用的確認 Modal
 * @param {object} options - 配置
 * @returns {Promise<boolean>} - resolve(true) if confirmed, resolve(false) otherwise
 */
function showConfirm({ title, message, confirmText = '確認', confirmClass = 'btn-primary' }) {
    return new Promise((resolve) => {
        const template = document.getElementById('confirmModalTemplate');
        if (!template) return resolve(false);

        const modalEl = template.content.firstElementChild.cloneNode(true);
        document.body.appendChild(modalEl);

        modalEl.querySelector('.modal-title').textContent = title;
        modalEl.querySelector('.modal-body').innerHTML = `<p>${message}</p>`;
        const confirmBtn = modalEl.querySelector('.btn-confirm');
        confirmBtn.textContent = confirmText;
        confirmBtn.className = `btn btn-confirm ${confirmClass}`;

        const modal = new bootstrap.Modal(modalEl);
        
        const onConfirm = () => {
            resolve(true);
            modal.hide();
        };
        const onCancel = () => resolve(false);

        confirmBtn.addEventListener('click', onConfirm);
        modalEl.addEventListener('hidden.bs.modal', () => {
            onCancel();
            modalEl.remove();
        }, { once: true });

        modal.show();
    });
}

/**
 * 顯示通知
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.setAttribute('role', 'alert');
    notification.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>`;
    document.body.appendChild(notification);
    setTimeout(() => bootstrap.Alert.getOrCreateInstance(notification)?.close(), 5000);
}

function showLoadingState() { /* ... 原函數不變 ... */ }
function hideLoadingState() { /* ... 原函數不變 ... */ }

// =====================================================
// 事件處理函數
// =====================================================

/**
 * 通用用戶操作處理 (批准, 拒絕, 刪除)
 */
async function handleUserAction(action, userId, confirmMessage, confirmText, confirmClass) {
    const confirmed = await showConfirm({
        title: `確認${confirmText}`,
        message: confirmMessage,
        confirmText: confirmText,
        confirmClass: confirmClass
    });

    if (!confirmed) return;

    const body = new URLSearchParams({ action, user_id: userId });
    try {
        const data = await apiCall(body);
        if (data.success) {
            showNotification(data.message, 'success');
            // 簡單地重新整理頁面來更新 UI
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showNotification(data.message, 'danger');
        }
    } catch (error) {
        // apiCall 內部已處理通知
    }
}

/**
 * 通用表單提交處理
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    const form = event.target;
    
    // 可以在此處添加表單驗證邏輯
    
    const formData = new FormData(form);
    const action = formData.get('action'); // 確保 form 中有 name="action" 的 input

    try {
        const data = await apiCall(formData);
        if (data.success) {
            showNotification(data.message, 'success');
            const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
            modal?.hide();
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showNotification(data.message, 'danger');
        }
    } catch (error) {
       // apiCall 內部已處理通知
    }
}

/**
 * 用戶搜索功能
 */
function handleUserSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('.admin-table tbody tr');
    
    tableRows.forEach(row => {
        const username = row.cells[1]?.textContent.toLowerCase() || '';
        const email = row.cells[2]?.textContent.toLowerCase() || '';
        row.style.display = (username.includes(searchTerm) || email.includes(searchTerm)) ? '' : 'none';
    });
}

// =====================================================
// Modal 開啟與數據填充
// =====================================================

function openEditUserModal(userData) {
    const modalEl = document.getElementById('editUserModal');
    if (!modalEl) return;

    // 填充表單
    Object.keys(userData).forEach(key => {
        const input = modalEl.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = userData[key] || '';
        }
    });
    // 特殊處理 user_id
    modalEl.querySelector('#edit_user_id').value = userData.id;

    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

function openAddUserModal() {
    const modalEl = document.getElementById('addUserModal');
    if (!modalEl) return;
    modalEl.querySelector('form').reset();
    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

function openCreditsModal(userId, currentCredits, username) {
    const modalEl = document.getElementById('creditsModal');
    if (!modalEl) return;

    modalEl.querySelector('form').reset();
    document.getElementById('credits_user_id').value = userId;
    document.getElementById('credits_username').value = username;
    document.getElementById('credits_current').value = currentCredits;
    
    const modal = new bootstrap.Modal(modalEl);
    modal.show();
}

// =====================================================
// 代理登入與試用管理 (這些函數保持不變，因為它們可能呼叫不同的 API 端點)
// =====================================================

async function proxyLogin(userId, username) {
    showLoadingState();
    try {
        const response = await fetch('php/admin_proxy_login.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'proxy_login', target_user_id: userId })
        });
        const data = await response.json();
        if (data.success) {
            showNotification(`已成功代理登入為 ${username}`, 'success');
            setTimeout(() => window.location.href = data.data.redirect_url, 1500);
        } else {
            showNotification(data.message || '代理登入失敗', 'danger');
        }
    } catch (error) {
        showNotification('網路錯誤: ' + error.message, 'danger');
    } finally {
        hideLoadingState();
    }
}

async function returnToAdmin() {
    showLoadingState();
    try {
        const response = await fetch('php/admin_proxy_login.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'return_to_admin' })
        });
        const data = await response.json();
        if (data.success) {
            window.location.href = data.data.redirect_url;
        } else {
            showNotification(data.message || '返回管理員失敗', 'danger');
        }
    } catch (error) {
        showNotification('網路錯誤: ' + error.message, 'danger');
    } finally {
        hideLoadingState();
    }
}

async function loadTrialStats() {
    try {
        const response = await fetch('php/admin_trial_management.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'action=get_trial_stats'
        });
        const data = await response.json();
        if (data.success) {
            const stats = data.data;
            document.getElementById('activeTrialsCount').textContent = stats.active_trials || 0;
            document.getElementById('expiringTrialsCount').textContent = stats.expiring_soon || 0;
            document.getElementById('expiredTrialsCount').textContent = stats.expired_trials || 0;
            document.getElementById('convertedTrialsCount').textContent = stats.converted_trials || 0;
        }
    } catch (error) {
        console.error('Error loading trial stats:', error);
    }
}

// 假設的 openTrialSettingsModal 函數
function openTrialSettingsModal() {
    // 這裡應有載入和顯示試用設定 Modal 的邏輯
    showNotification('試用設定功能待實現。', 'info');
}