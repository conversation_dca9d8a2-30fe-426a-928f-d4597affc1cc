<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSRF Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 3px; cursor: pointer; background: #007bff; color: white; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 3px; overflow-x: auto; }
        #results { min-height: 200px; }
    </style>
</head>
<body>
    <h1>🔍 CSRF Debug Test</h1>
    
    <div class="debug-section">
        <h3>Current Page Info</h3>
        <div id="page-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>CSRF Token in Meta:</strong> <span id="csrf-meta"></span></p>
            <p><strong>Token Length:</strong> <span id="token-length"></span></p>
        </div>
    </div>

    <div class="debug-section">
        <h3>Test Actions</h3>
        <button onclick="testSessionInfo()">Test Session Info</button>
        <button onclick="testCSRFValidation()">Test CSRF Validation</button>
        <button onclick="testAdminAPI()">Test Admin API</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="debug-section">
        <h3>Results</h3>
        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testSessionInfo() {
            log('Testing session information...');
            try {
                const response = await fetch('debug_csrf.php', {
                    method: 'GET'
                });
                const data = await response.json();
                log('Session Info:', 'success');
                log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
            } catch (error) {
                log('Error getting session info: ' + error.message, 'error');
            }
        }

        async function testCSRFValidation() {
            log('Testing CSRF validation...');
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                log('❌ No CSRF token found in meta tag', 'error');
                return;
            }

            const token = csrfToken.getAttribute('content');
            log('✓ CSRF token found: ' + token.substring(0, 10) + '...', 'success');

            try {
                const formData = new FormData();
                formData.append('csrf_token', token);
                formData.append('action', 'test');

                const response = await fetch('debug_csrf.php', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                log('CSRF Validation Result:', 'success');
                log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
            } catch (error) {
                log('Error testing CSRF validation: ' + error.message, 'error');
            }
        }

        async function testAdminAPI() {
            log('Testing Admin API call...');
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                log('❌ No CSRF token found in meta tag', 'error');
                return;
            }

            const token = csrfToken.getAttribute('content');
            
            try {
                const formData = new FormData();
                formData.append('csrf_token', token);
                formData.append('action', 'test_action');

                log('Sending request to admin_api.php with token: ' + token.substring(0, 10) + '...');

                const response = await fetch('admin_api.php', {
                    method: 'POST',
                    body: formData
                });

                log('Response status: ' + response.status);
                
                const data = await response.json();
                log('Admin API Response:', response.ok ? 'success' : 'error');
                log('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
            } catch (error) {
                log('Error testing Admin API: ' + error.message, 'error');
            }
        }

        // Initialize page info
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('current-url').textContent = window.location.href;
            
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const token = csrfToken.getAttribute('content');
                document.getElementById('csrf-meta').textContent = token.substring(0, 20) + '...';
                document.getElementById('token-length').textContent = token.length + ' characters';
            } else {
                document.getElementById('csrf-meta').textContent = 'NOT FOUND';
                document.getElementById('token-length').textContent = '0 characters';
            }

            log('🚀 CSRF Debug page loaded');
            log('Ready to test CSRF token functionality');
        });
    </script>
</body>
</html>
