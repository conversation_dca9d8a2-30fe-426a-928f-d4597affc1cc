# 管理員代理登入功能指南
## KMS Receipt Maker - Admin Proxy Login Feature Guide

### 🎯 功能概述

管理員代理登入功能允許管理員無須密碼直接登入會員帳號，用於客戶支援、問題排查和帳號管理。此功能包含完整的安全機制和操作日誌記錄。

### 🚀 主要特色

1. **無密碼登入**：管理員可直接登入任何活躍會員帳號
2. **安全會話管理**：完整的會話驗證和過期機制
3. **操作日誌記錄**：所有代理操作都會被詳細記錄
4. **一鍵返回**：可隨時返回原始管理員帳號
5. **視覺化指示**：清楚顯示當前處於代理會話狀態
6. **多語言支持**：完整的中英文界面

### 📋 安裝步驟

#### 1. 數據庫設置
```bash
# 在 MySQL 中執行以下腳本
mysql -u root -p kms_receipt_maker < database/add_admin_proxy_system.sql
```

#### 2. 檔案檢查
確保以下檔案存在：
- `php/admin_proxy_login.php` - 代理登入處理器
- `php/ProxySessionManager.php` - 會話管理器
- `database/add_admin_proxy_system.sql` - 數據庫腳本

#### 3. 權限設置
確保 PHP 有權限讀寫會話檔案和數據庫。

### 🎮 使用方法

#### 管理員操作

1. **登入管理員帳號**
   - 使用管理員帳號登入系統
   - 進入 Admin Panel

2. **代理登入會員**
   - 在 User Management 表格中找到目標會員
   - 點擊 "🔑 Login As" 按鈕
   - 確認代理登入操作
   - 系統會自動跳轉到會員視角

3. **返回管理員帳號**
   - 在代理會話中，導航欄會顯示 "🔙 Return to Admin" 按鈕
   - 點擊按鈕確認返回
   - 系統會自動跳轉回管理員面板

#### 會話狀態指示

- **代理會話指示器**：顯示當前處於代理會話，並顯示原始管理員用戶名
- **返回按鈕**：醒目的橙紅色按鈕，隨時可返回管理員帳號
- **會話警告**：接近過期時會顯示警告

### 🔒 安全機制

#### 會話管理
- **會話過期**：預設1小時後自動過期
- **身份驗證**：持續驗證原始管理員身份
- **狀態檢查**：確保目標用戶仍然有效

#### 操作限制
- 只能代理登入 `member` 類型的用戶
- 不能代理登入其他管理員帳號
- 只能代理登入 `active` 狀態的用戶
- 需要確認操作才能執行

#### 日誌記錄
所有操作都會記錄在 `admin_proxy_logs` 表中：
- 代理登入時間和IP
- 會話持續時間
- 返回管理員操作
- 異常情況（過期、無效等）

### 📊 數據庫結構

#### admin_proxy_logs 表
```sql
- id: 日誌ID
- admin_id: 執行操作的管理員ID
- target_user_id: 目標用戶ID
- action: 操作類型 (proxy_login, return_to_admin, session_expired等)
- ip_address: IP地址
- user_agent: 瀏覽器信息
- session_duration: 會話持續時間（秒）
- created_at: 操作時間
```

#### admin_proxy_settings 表
```sql
- setting_key: 設置鍵
- setting_value: 設置值
- description: 設置描述
```

### ⚙️ 配置選項

可在 `admin_proxy_settings` 表中調整以下設置：

- `enable_proxy_login`: 啟用/禁用代理登入功能
- `max_proxy_session_duration`: 最大會話持續時間（秒）
- `proxy_session_warning_time`: 會話過期警告時間（秒）
- `allowed_proxy_user_types`: 允許代理登入的用戶類型
- `require_confirmation`: 是否需要確認操作

### 🎨 界面元素

#### CSS 類別
- `.proxy-session-indicator`: 代理會話指示器樣式
- `.return-admin-btn`: 返回管理員按鈕樣式
- `.pulse-glow`: 脈衝發光動畫

#### JavaScript 函數
- `proxyLogin(userId, username)`: 執行代理登入
- `returnToAdmin()`: 返回管理員帳號

### 🌐 多語言支持

#### 中文翻譯鍵
- `proxy_session`: 代理會話
- `return_to_admin`: 返回管理員
- `login_as_user`: 登入為用戶
- `proxy_login_confirm`: 確定要登入為此用戶嗎？

#### 英文翻譯鍵
- `proxy_session`: Proxy Session
- `return_to_admin`: Return to Admin
- `login_as_user`: Login as User
- `proxy_login_confirm`: Are you sure you want to login as this user?

### 🧪 測試建議

1. **基本功能測試**
   - 管理員代理登入會員帳號
   - 在會員視角下操作系統
   - 返回管理員帳號

2. **安全性測試**
   - 嘗試代理登入管理員帳號（應該失敗）
   - 測試會話過期機制
   - 檢查操作日誌記錄

3. **界面測試**
   - 檢查代理會話指示器顯示
   - 測試返回按鈕功能
   - 驗證多語言切換

### 🚨 注意事項

1. **安全性**
   - 此功能僅供管理員使用，請謹慎授權
   - 所有操作都會被記錄，請遵守隱私政策
   - 建議定期檢查操作日誌

2. **性能**
   - 代理會話會增加服務器負載
   - 建議設置合理的會話過期時間
   - 定期清理過期的日誌記錄

3. **合規性**
   - 確保符合當地的數據保護法規
   - 建議在使用條款中說明此功能
   - 考慮實施額外的審計機制

### 📞 支援

如有問題或需要協助，請聯繫系統管理員或查看相關文檔。

---

**版本**: 1.0  
**更新日期**: 2025-01-13  
**作者**: KMS Receipt Maker Development Team
