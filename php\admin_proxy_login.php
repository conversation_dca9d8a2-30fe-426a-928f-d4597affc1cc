<?php
/**
 * 管理員代理登入功能
 * KMS Receipt Maker - Admin Proxy Login
 * 
 * 允許管理員無須密碼登入會員帳號，並提供安全的會話管理
 */

session_start();

require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';
require_once 'ProxySessionManager.php';

// 檢查是否為管理員或處於代理會話中
$isAdmin = isset($_SESSION['user_id']) && $_SESSION['user_type'] === 'admin';
$isProxySession = isset($_SESSION['is_proxy_session']) && $_SESSION['is_proxy_session'] && isset($_SESSION['original_admin_id']);

if (!$isAdmin && !$isProxySession) {
    Response::error('Access denied. Admin privileges required.');
    exit;
}

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('Method not allowed');
    exit;
}

$action = $_POST['action'] ?? '';

try {
    $db = new Database();
    $conn = $db->getConnection();
    $proxyManager = new ProxySessionManager();
    
    switch ($action) {
        case 'proxy_login':
            // 只有真正的管理員可以執行代理登入
            if (!$isAdmin) {
                Response::error('Only admin can perform proxy login');
                exit;
            }

            $target_user_id = intval($_POST['target_user_id'] ?? 0);

            if ($target_user_id <= 0) {
                Response::error('Invalid user ID');
                exit;
            }
            
            // 查詢目標用戶
            $stmt = $conn->prepare("
                SELECT id, username, email, user_type, status, email_verified 
                FROM users 
                WHERE id = ? AND user_type != 'admin'
            ");
            $stmt->bind_param('i', $target_user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($target_user = $result->fetch_assoc()) {
                // 檢查用戶狀態
                if ($target_user['status'] !== 'active') {
                    Response::error('Cannot login as inactive user');
                    exit;
                }
                
                // 保存原始管理員信息到會話中
                $_SESSION['original_admin_id'] = $_SESSION['user_id'];
                $_SESSION['original_admin_username'] = $_SESSION['username'];
                $_SESSION['original_admin_type'] = $_SESSION['user_type'];
                $_SESSION['is_proxy_session'] = true;
                $_SESSION['proxy_start_time'] = time();
                
                // 切換到目標用戶身份
                $_SESSION['user_id'] = $target_user['id'];
                $_SESSION['username'] = $target_user['username'];
                $_SESSION['user_type'] = $target_user['user_type'];
                
                // 記錄代理登入日誌
                $log_stmt = $conn->prepare("
                    INSERT INTO admin_proxy_logs (admin_id, target_user_id, action, ip_address, user_agent, created_at) 
                    VALUES (?, ?, 'proxy_login', ?, ?, NOW())
                ");
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $log_stmt->bind_param('iiss', $_SESSION['original_admin_id'], $target_user_id, $ip_address, $user_agent);
                $log_stmt->execute();
                
                Response::success([
                    'redirect_url' => 'index.php',
                    'target_user' => [
                        'id' => $target_user['id'],
                        'username' => $target_user['username'],
                        'email' => $target_user['email']
                    ]
                ], 'Successfully logged in as ' . $target_user['username']);
                
            } else {
                Response::error('User not found or cannot proxy login to admin accounts');
            }
            break;
            
        case 'return_to_admin':
            if (!isset($_SESSION['is_proxy_session']) || !isset($_SESSION['original_admin_id'])) {
                Response::error('Not in proxy session');
                exit;
            }
            
            $original_admin_id = $_SESSION['original_admin_id'];
            $current_user_id = $_SESSION['user_id'];
            
            // 查詢原始管理員信息
            $stmt = $conn->prepare("
                SELECT id, username, user_type 
                FROM users 
                WHERE id = ? AND user_type = 'admin'
            ");
            $stmt->bind_param('i', $original_admin_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($admin_user = $result->fetch_assoc()) {
                // 記錄返回管理員日誌
                $log_stmt = $conn->prepare("
                    INSERT INTO admin_proxy_logs (admin_id, target_user_id, action, ip_address, user_agent, created_at) 
                    VALUES (?, ?, 'return_to_admin', ?, ?, NOW())
                ");
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $log_stmt->bind_param('iiss', $original_admin_id, $current_user_id, $ip_address, $user_agent);
                $log_stmt->execute();
                
                // 恢復管理員身份
                $_SESSION['user_id'] = $admin_user['id'];
                $_SESSION['username'] = $admin_user['username'];
                $_SESSION['user_type'] = $admin_user['user_type'];
                
                // 清除代理會話標記
                unset($_SESSION['original_admin_id']);
                unset($_SESSION['original_admin_username']);
                unset($_SESSION['original_admin_type']);
                unset($_SESSION['is_proxy_session']);
                unset($_SESSION['proxy_start_time']);
                
                Response::success([
                    'redirect_url' => 'admin.php'
                ], 'Successfully returned to admin account');
                
            } else {
                Response::error('Original admin account not found');
            }
            break;
            
        case 'get_proxy_status':
            $status = [
                'is_proxy_session' => isset($_SESSION['is_proxy_session']) && $_SESSION['is_proxy_session'],
                'original_admin_username' => $_SESSION['original_admin_username'] ?? null,
                'current_username' => $_SESSION['username'] ?? null,
                'proxy_duration' => isset($_SESSION['proxy_start_time']) ? (time() - $_SESSION['proxy_start_time']) : 0
            ];
            
            Response::success($status, 'Proxy status retrieved');
            break;
            
        default:
            Response::error('Invalid action');
            break;
    }
    
} catch (Exception $e) {
    error_log("Admin proxy login error: " . $e->getMessage());
    Response::error('System error occurred');
}
?>
